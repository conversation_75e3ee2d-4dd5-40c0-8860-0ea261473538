{"ast": null, "code": "import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge(...args) {\n  const scheduler = popScheduler(args);\n  const concurrent = popNumber(args, Infinity);\n  const sources = args;\n  return !sources.length ? EMPTY : sources.length === 1 ? innerFrom(sources[0]) : mergeAll(concurrent)(from(sources, scheduler));\n}", "map": {"version": 3, "names": ["mergeAll", "innerFrom", "EMPTY", "popNumber", "popScheduler", "from", "merge", "args", "scheduler", "concurrent", "Infinity", "sources", "length"], "sources": ["C:/Users/<USER>/Desktop/dhia b2b/angular/e-front/node_modules/rxjs/dist/esm/internal/observable/merge.js"], "sourcesContent": ["import { mergeAll } from '../operators/mergeAll';\nimport { innerFrom } from './innerFrom';\nimport { EMPTY } from './empty';\nimport { popNumber, popScheduler } from '../util/args';\nimport { from } from './from';\nexport function merge(...args) {\n    const scheduler = popScheduler(args);\n    const concurrent = popNumber(args, Infinity);\n    const sources = args;\n    return !sources.length\n        ?\n            EMPTY\n        : sources.length === 1\n            ?\n                innerFrom(sources[0])\n            :\n                mergeAll(concurrent)(from(sources, scheduler));\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,uBAAuB;AAChD,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,QAAQ,QAAQ;AAC7B,OAAO,SAASC,KAAKA,CAAC,GAAGC,IAAI,EAAE;EAC3B,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAI,CAAC;EACpC,MAAME,UAAU,GAAGN,SAAS,CAACI,IAAI,EAAEG,QAAQ,CAAC;EAC5C,MAAMC,OAAO,GAAGJ,IAAI;EACpB,OAAO,CAACI,OAAO,CAACC,MAAM,GAEdV,KAAK,GACPS,OAAO,CAACC,MAAM,KAAK,CAAC,GAEdX,SAAS,CAACU,OAAO,CAAC,CAAC,CAAC,CAAC,GAErBX,QAAQ,CAACS,UAAU,CAAC,CAACJ,IAAI,CAACM,OAAO,EAAEH,SAAS,CAAC,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}