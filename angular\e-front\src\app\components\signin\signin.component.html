<div class="signin-container">
  <div class="signin-card">
    <!-- Header -->
    <div class="signin-header">
      <div class="logo-container">
        <div class="logo">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
      </div>
      <h1 class="signin-title">Agency Portal</h1>
      <p class="signin-subtitle">Sign in to your agency account</p>
    </div>

    <!-- Error Message -->
    <div class="error-container" *ngIf="errorMessage">
      <div class="error-message">
        <svg class="error-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
          <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
        </svg>
        <span>{{ errorMessage }}</span>
        <button type="button" class="error-close" (click)="clearError()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" stroke-width="2"/>
            <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" stroke-width="2"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Sign-in Form -->
    <form [formGroup]="signinForm" (ngSubmit)="onSubmit()" class="signin-form">
      <!-- Agency Field -->
      <div class="form-group">
        <label for="agency" class="form-label">Agency Code</label>
        <div class="input-container">
          <input
            type="text"
            id="agency"
            formControlName="agency"
            class="form-input"
            [class.error]="hasError('agency', 'required') || hasError('agency', 'minlength')"
            placeholder="Enter your agency code"
            autocomplete="organization"
          />
          <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="error-text" *ngIf="hasError('agency', 'required') || hasError('agency', 'minlength')">
          {{ getErrorMessage('agency') }}
        </div>
      </div>

      <!-- Username Field -->
      <div class="form-group">
        <label for="username" class="form-label">Username</label>
        <div class="input-container">
          <input
            type="text"
            id="username"
            formControlName="username"
            class="form-input"
            [class.error]="hasError('username', 'required') || hasError('username', 'minlength')"
            placeholder="Enter your username"
            autocomplete="username"
          />
          <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </div>
        <div class="error-text" *ngIf="hasError('username', 'required') || hasError('username', 'minlength')">
          {{ getErrorMessage('username') }}
        </div>
      </div>

      <!-- Password Field -->
      <div class="form-group">
        <label for="password" class="form-label">Password</label>
        <div class="input-container">
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            formControlName="password"
            class="form-input"
            [class.error]="hasError('password', 'required') || hasError('password', 'minlength')"
            placeholder="Enter your password"
            autocomplete="current-password"
          />
          <svg class="input-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2"/>
            <path d="M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11" stroke="currentColor" stroke-width="2"/>
          </svg>
          <button
            type="button"
            class="password-toggle"
            (click)="togglePasswordVisibility()"
            [attr.aria-label]="showPassword ? 'Hide password' : 'Show password'"
          >
            <svg *ngIf="!showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
            </svg>
            <svg *ngIf="showPassword" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
              <path d="M1 1L23 23" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>
        <div class="error-text" *ngIf="hasError('password', 'required') || hasError('password', 'minlength')">
          {{ getErrorMessage('password') }}
        </div>
      </div>

      <!-- Submit Button -->
      <button
        type="submit"
        class="signin-button"
        [disabled]="isLoading || signinForm.invalid"
        [class.loading]="isLoading"
      >
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading" class="loading-content">
          <svg class="loading-spinner" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2"/>
            <path d="M12 3C16.9706 3 21 7.02944 21 12" stroke="url(#spinner-gradient)" stroke-width="2" stroke-linecap="round"/>
            <defs>
              <linearGradient id="spinner-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" style="stop-color:currentColor;stop-opacity:0" />
                <stop offset="100%" style="stop-color:currentColor;stop-opacity:1" />
              </linearGradient>
            </defs>
          </svg>
          Signing in...
        </span>
      </button>
    </form>

    <!-- Footer -->
    <div class="signin-footer">
      <p class="footer-text">
        Need help? Contact your system administrator
      </p>
    </div>
  </div>

  <!-- Background decoration -->
  <div class="background-decoration">
    <div class="decoration-circle decoration-circle-1"></div>
    <div class="decoration-circle decoration-circle-2"></div>
    <div class="decoration-circle decoration-circle-3"></div>
  </div>
</div>
