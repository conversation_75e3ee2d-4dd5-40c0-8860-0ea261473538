import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, BehaviorSubject, throwError } from 'rxjs';
import { map, catchError, tap } from 'rxjs/operators';
import { AuthRequest, AuthResponse, LoginCredentials, LoginResponse } from '../models/auth.models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly API_URL = 'http://localhost:8080/auth';
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'user_data';

  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();

  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * Authenticate user with agency credentials
   */
  login(credentials: LoginCredentials): Observable<LoginResponse> {
    const authRequest: AuthRequest = {
      Agency: credentials.agency,
      User: credentials.username,
      Password: credentials.password
    };

    const headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    return this.http.post<AuthResponse>(`${this.API_URL}/login`, authRequest, { headers })
      .pipe(
        map(response => {
          if (response.header.success && response.body.token) {
            // Store token and user data
            this.setToken(response.body.token);
            const userData = {
              id: response.body.user.id,
              name: response.body.user.name,
              email: response.body.user.email,
              agency: response.body.agency.name,
              agencyCode: response.body.agency.code
            };
            this.setUserData(userData);
            
            // Update subjects
            this.isAuthenticatedSubject.next(true);
            this.currentUserSubject.next(userData);

            return {
              success: true,
              token: response.body.token,
              user: userData,
              message: 'Login successful'
            };
          } else {
            throw new Error(response.header.responseMessage || 'Authentication failed');
          }
        }),
        catchError(error => {
          console.error('Login error:', error);
          let errorMessage = 'An error occurred during login';
          
          if (error.error?.header?.responseMessage) {
            errorMessage = error.error.header.responseMessage;
          } else if (error.message) {
            errorMessage = error.message;
          } else if (error.status === 401) {
            errorMessage = 'Invalid credentials';
          } else if (error.status === 0) {
            errorMessage = 'Unable to connect to server';
          }

          return throwError(() => ({
            success: false,
            message: errorMessage
          }));
        })
      );
  }

  /**
   * Logout user
   */
  logout(): void {
    this.removeToken();
    this.removeUserData();
    this.isAuthenticatedSubject.next(false);
    this.currentUserSubject.next(null);
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return this.hasToken() && !this.isTokenExpired();
  }

  /**
   * Get current user data
   */
  getCurrentUser(): any {
    const userData = localStorage.getItem(this.USER_KEY);
    return userData ? JSON.parse(userData) : null;
  }

  /**
   * Get authentication token
   */
  getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Set authentication token
   */
  private setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  /**
   * Remove authentication token
   */
  private removeToken(): void {
    localStorage.removeItem(this.TOKEN_KEY);
  }

  /**
   * Set user data
   */
  private setUserData(userData: any): void {
    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));
  }

  /**
   * Remove user data
   */
  private removeUserData(): void {
    localStorage.removeItem(this.USER_KEY);
  }

  /**
   * Check if token exists
   */
  private hasToken(): boolean {
    return !!localStorage.getItem(this.TOKEN_KEY);
  }

  /**
   * Check if token is expired (basic check)
   * Note: This is a simple implementation. In production, you should decode the JWT token
   * and check the expiration time properly.
   */
  private isTokenExpired(): boolean {
    // For now, we'll assume the token is valid if it exists
    // In a real implementation, you would decode the JWT and check the exp claim
    return false;
  }

  /**
   * Get authorization headers for API calls
   */
  getAuthHeaders(): HttpHeaders {
    const token = this.getToken();
    return new HttpHeaders({
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    });
  }
}
