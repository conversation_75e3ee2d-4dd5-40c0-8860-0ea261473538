{"ast": null, "code": "import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n  const tapObserver = isFunction(observerOrNext) || error || complete ? {\n    next: observerOrNext,\n    error,\n    complete\n  } : observerOrNext;\n  return tapObserver ? operate((source, subscriber) => {\n    var _a;\n    (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n    let isUnsub = true;\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      var _a;\n      (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n      subscriber.next(value);\n    }, () => {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      subscriber.complete();\n    }, err => {\n      var _a;\n      isUnsub = false;\n      (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n      subscriber.error(err);\n    }, () => {\n      var _a, _b;\n      if (isUnsub) {\n        (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n      }\n      (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n    }));\n  }) : identity;\n}", "map": {"version": 3, "names": ["isFunction", "operate", "createOperatorSubscriber", "identity", "tap", "observerOrNext", "error", "complete", "tapObserver", "next", "source", "subscriber", "_a", "subscribe", "call", "isUnsub", "value", "err", "_b", "unsubscribe", "finalize"], "sources": ["C:/Users/<USER>/Desktop/dhia b2b/angular/e-front/node_modules/rxjs/dist/esm/internal/operators/tap.js"], "sourcesContent": ["import { isFunction } from '../util/isFunction';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { identity } from '../util/identity';\nexport function tap(observerOrNext, error, complete) {\n    const tapObserver = isFunction(observerOrNext) || error || complete\n        ?\n            { next: observerOrNext, error, complete }\n        : observerOrNext;\n    return tapObserver\n        ? operate((source, subscriber) => {\n            var _a;\n            (_a = tapObserver.subscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n            let isUnsub = true;\n            source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n                var _a;\n                (_a = tapObserver.next) === null || _a === void 0 ? void 0 : _a.call(tapObserver, value);\n                subscriber.next(value);\n            }, () => {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.complete) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                subscriber.complete();\n            }, (err) => {\n                var _a;\n                isUnsub = false;\n                (_a = tapObserver.error) === null || _a === void 0 ? void 0 : _a.call(tapObserver, err);\n                subscriber.error(err);\n            }, () => {\n                var _a, _b;\n                if (isUnsub) {\n                    (_a = tapObserver.unsubscribe) === null || _a === void 0 ? void 0 : _a.call(tapObserver);\n                }\n                (_b = tapObserver.finalize) === null || _b === void 0 ? void 0 : _b.call(tapObserver);\n            }));\n        })\n        :\n            identity;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,GAAGA,CAACC,cAAc,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACjD,MAAMC,WAAW,GAAGR,UAAU,CAACK,cAAc,CAAC,IAAIC,KAAK,IAAIC,QAAQ,GAE3D;IAAEE,IAAI,EAAEJ,cAAc;IAAEC,KAAK;IAAEC;EAAS,CAAC,GAC3CF,cAAc;EACpB,OAAOG,WAAW,GACZP,OAAO,CAAC,CAACS,MAAM,EAAEC,UAAU,KAAK;IAC9B,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGJ,WAAW,CAACK,SAAS,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,WAAW,CAAC;IACtF,IAAIO,OAAO,GAAG,IAAI;IAClBL,MAAM,CAACG,SAAS,CAACX,wBAAwB,CAACS,UAAU,EAAGK,KAAK,IAAK;MAC7D,IAAIJ,EAAE;MACN,CAACA,EAAE,GAAGJ,WAAW,CAACC,IAAI,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,WAAW,EAAEQ,KAAK,CAAC;MACxFL,UAAU,CAACF,IAAI,CAACO,KAAK,CAAC;IAC1B,CAAC,EAAE,MAAM;MACL,IAAIJ,EAAE;MACNG,OAAO,GAAG,KAAK;MACf,CAACH,EAAE,GAAGJ,WAAW,CAACD,QAAQ,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,WAAW,CAAC;MACrFG,UAAU,CAACJ,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAGU,GAAG,IAAK;MACR,IAAIL,EAAE;MACNG,OAAO,GAAG,KAAK;MACf,CAACH,EAAE,GAAGJ,WAAW,CAACF,KAAK,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,WAAW,EAAES,GAAG,CAAC;MACvFN,UAAU,CAACL,KAAK,CAACW,GAAG,CAAC;IACzB,CAAC,EAAE,MAAM;MACL,IAAIL,EAAE,EAAEM,EAAE;MACV,IAAIH,OAAO,EAAE;QACT,CAACH,EAAE,GAAGJ,WAAW,CAACW,WAAW,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,IAAI,CAACN,WAAW,CAAC;MAC5F;MACA,CAACU,EAAE,GAAGV,WAAW,CAACY,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACJ,IAAI,CAACN,WAAW,CAAC;IACzF,CAAC,CAAC,CAAC;EACP,CAAC,CAAC,GAEEL,QAAQ;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}