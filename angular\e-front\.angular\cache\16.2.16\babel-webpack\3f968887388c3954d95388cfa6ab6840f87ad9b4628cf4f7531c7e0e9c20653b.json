{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nfunction SigninComponent_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(2, \"svg\", 41);\n    i0.ɵɵelement(3, \"circle\", 42)(4, \"line\", 43)(5, \"line\", 44);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function SigninComponent_div_13_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r8.clearError());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 46);\n    i0.ɵɵelement(10, \"line\", 47)(11, \"line\", 48);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.errorMessage);\n  }\n}\nfunction SigninComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(\"agency\"), \" \");\n  }\n}\nfunction SigninComponent_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(\"username\"), \" \");\n  }\n}\nfunction SigninComponent__svg_svg_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 50);\n    i0.ɵɵelement(1, \"path\", 51)(2, \"circle\", 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent__svg_svg_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 50);\n    i0.ɵɵelement(1, \"path\", 53)(2, \"path\", 54)(3, \"circle\", 52)(4, \"path\", 55);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r5.getErrorMessage(\"password\"), \" \");\n  }\n}\nfunction SigninComponent_span_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Sign In\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction SigninComponent_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 56);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 57);\n    i0.ɵɵelement(2, \"path\", 58)(3, \"path\", 59);\n    i0.ɵɵelementStart(4, \"defs\")(5, \"linearGradient\", 60);\n    i0.ɵɵelement(6, \"stop\", 61)(7, \"stop\", 62);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtext(8, \" Signing in... \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class SigninComponent {\n  constructor(formBuilder, authService, router) {\n    this.formBuilder = formBuilder;\n    this.authService = authService;\n    this.router = router;\n    this.isLoading = false;\n    this.errorMessage = '';\n    this.showPassword = false;\n    this.signinForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // If user is already authenticated, redirect to dashboard\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  /**\n   * Handle form submission\n   */\n  onSubmit() {\n    if (this.signinForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n      const credentials = {\n        agency: this.signinForm.value.agency.trim(),\n        username: this.signinForm.value.username.trim(),\n        password: this.signinForm.value.password\n      };\n      this.authService.login(credentials).subscribe({\n        next: response => {\n          if (response.success) {\n            // Redirect to dashboard or intended route\n            this.router.navigate(['/dashboard']);\n          }\n        },\n        error: error => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n          // Clear password field on error\n          this.signinForm.patchValue({\n            password: ''\n          });\n        },\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility() {\n    this.showPassword = !this.showPassword;\n  }\n  /**\n   * Mark all form fields as touched to show validation errors\n   */\n  markFormGroupTouched() {\n    Object.keys(this.signinForm.controls).forEach(key => {\n      const control = this.signinForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  /**\n   * Get form control for easier access in template\n   */\n  getFormControl(controlName) {\n    return this.signinForm.get(controlName);\n  }\n  /**\n   * Check if form control has error\n   */\n  hasError(controlName, errorType) {\n    const control = this.getFormControl(controlName);\n    return !!(control?.hasError(errorType) && control?.touched);\n  }\n  /**\n   * Get error message for form control\n   */\n  getErrorMessage(controlName) {\n    const control = this.getFormControl(controlName);\n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(controlName)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;\n    }\n    return '';\n  }\n  /**\n   * Get display name for form field\n   */\n  getFieldDisplayName(controlName) {\n    const fieldNames = {\n      agency: 'Agency',\n      username: 'Username',\n      password: 'Password'\n    };\n    return fieldNames[controlName] || controlName;\n  }\n  /**\n   * Clear error message\n   */\n  clearError() {\n    this.errorMessage = '';\n  }\n  static {\n    this.ɵfac = function SigninComponent_Factory(t) {\n      return new (t || SigninComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SigninComponent,\n      selectors: [[\"app-signin\"]],\n      decls: 56,\n      vars: 20,\n      consts: [[1, \"signin-container\"], [1, \"signin-card\"], [1, \"signin-header\"], [1, \"logo-container\"], [1, \"logo\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 2L2 7L12 12L22 7L12 2Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M2 17L12 22L22 17\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M2 12L12 17L22 12\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"signin-title\"], [1, \"signin-subtitle\"], [\"class\", \"error-container\", 4, \"ngIf\"], [1, \"signin-form\", 3, \"formGroup\", \"ngSubmit\"], [1, \"form-group\"], [\"for\", \"agency\", 1, \"form-label\"], [1, \"input-container\"], [\"type\", \"text\", \"id\", \"agency\", \"formControlName\", \"agency\", \"placeholder\", \"Enter your agency code\", \"autocomplete\", \"organization\", 1, \"form-input\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"input-icon\"], [\"d\", \"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"cx\", \"12\", \"cy\", \"7\", \"r\", \"4\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"class\", \"error-text\", 4, \"ngIf\"], [\"for\", \"username\", 1, \"form-label\"], [\"type\", \"text\", \"id\", \"username\", \"formControlName\", \"username\", \"placeholder\", \"Enter your username\", \"autocomplete\", \"username\", 1, \"form-input\"], [\"for\", \"password\", 1, \"form-label\"], [\"id\", \"password\", \"formControlName\", \"password\", \"placeholder\", \"Enter your password\", \"autocomplete\", \"current-password\", 1, \"form-input\", 3, \"type\"], [\"x\", \"3\", \"y\", \"11\", \"width\", \"18\", \"height\", \"11\", \"rx\", \"2\", \"ry\", \"2\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"cx\", \"12\", \"cy\", \"16\", \"r\", \"1\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"type\", \"button\", 1, \"password-toggle\", 3, \"click\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 4, \"ngIf\"], [\"type\", \"submit\", 1, \"signin-button\", 3, \"disabled\"], [4, \"ngIf\"], [\"class\", \"loading-content\", 4, \"ngIf\"], [1, \"signin-footer\"], [1, \"footer-text\"], [1, \"background-decoration\"], [1, \"decoration-circle\", \"decoration-circle-1\"], [1, \"decoration-circle\", \"decoration-circle-2\"], [1, \"decoration-circle\", \"decoration-circle-3\"], [1, \"error-container\"], [1, \"error-message\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"error-icon\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"10\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"15\", \"y1\", \"9\", \"x2\", \"9\", \"y2\", \"15\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"9\", \"y1\", \"9\", \"x2\", \"15\", \"y2\", \"15\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"type\", \"button\", 1, \"error-close\", 3, \"click\"], [\"width\", \"16\", \"height\", \"16\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"x1\", \"18\", \"y1\", \"6\", \"x2\", \"6\", \"y2\", \"18\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"x1\", \"6\", \"y1\", \"6\", \"x2\", \"18\", \"y2\", \"18\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [1, \"error-text\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"cx\", \"12\", \"cy\", \"12\", \"r\", \"3\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [\"d\", \"M1 1L23 23\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\", \"stroke-linejoin\", \"round\"], [1, \"loading-content\"], [\"width\", \"20\", \"height\", \"20\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\", 1, \"loading-spinner\"], [\"d\", \"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\", \"stroke\", \"currentColor\", \"stroke-width\", \"2\"], [\"d\", \"M12 3C16.9706 3 21 7.02944 21 12\", \"stroke\", \"url(#spinner-gradient)\", \"stroke-width\", \"2\", \"stroke-linecap\", \"round\"], [\"id\", \"spinner-gradient\", \"x1\", \"0%\", \"y1\", \"0%\", \"x2\", \"100%\", \"y2\", \"0%\"], [\"offset\", \"0%\", 2, \"stop-color\", \"currentColor\", \"stop-opacity\", \"0\"], [\"offset\", \"100%\", 2, \"stop-color\", \"currentColor\", \"stop-opacity\", \"1\"]],\n      template: function SigninComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(5, \"svg\", 5);\n          i0.ɵɵelement(6, \"path\", 6)(7, \"path\", 7)(8, \"path\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(9, \"h1\", 9);\n          i0.ɵɵtext(10, \"Agency Portal\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"p\", 10);\n          i0.ɵɵtext(12, \"Sign in to your agency account\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(13, SigninComponent_div_13_Template, 12, 1, \"div\", 11);\n          i0.ɵɵelementStart(14, \"form\", 12);\n          i0.ɵɵlistener(\"ngSubmit\", function SigninComponent_Template_form_ngSubmit_14_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(15, \"div\", 13)(16, \"label\", 14);\n          i0.ɵɵtext(17, \"Agency Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 15);\n          i0.ɵɵelement(19, \"input\", 16);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(20, \"svg\", 17);\n          i0.ɵɵelement(21, \"path\", 18)(22, \"circle\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(23, SigninComponent_div_23_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(24, \"div\", 13)(25, \"label\", 21);\n          i0.ɵɵtext(26, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 15);\n          i0.ɵɵelement(28, \"input\", 22);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(29, \"svg\", 17);\n          i0.ɵɵelement(30, \"path\", 18)(31, \"circle\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(32, SigninComponent_div_32_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(33, \"div\", 13)(34, \"label\", 23);\n          i0.ɵɵtext(35, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(36, \"div\", 15);\n          i0.ɵɵelement(37, \"input\", 24);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(38, \"svg\", 17);\n          i0.ɵɵelement(39, \"rect\", 25)(40, \"circle\", 26)(41, \"path\", 27);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelementStart(42, \"button\", 28);\n          i0.ɵɵlistener(\"click\", function SigninComponent_Template_button_click_42_listener() {\n            return ctx.togglePasswordVisibility();\n          });\n          i0.ɵɵtemplate(43, SigninComponent__svg_svg_43_Template, 3, 0, \"svg\", 29);\n          i0.ɵɵtemplate(44, SigninComponent__svg_svg_44_Template, 5, 0, \"svg\", 29);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(45, SigninComponent_div_45_Template, 2, 1, \"div\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 30);\n          i0.ɵɵtemplate(47, SigninComponent_span_47_Template, 2, 0, \"span\", 31);\n          i0.ɵɵtemplate(48, SigninComponent_span_48_Template, 9, 0, \"span\", 32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"div\", 33)(50, \"p\", 34);\n          i0.ɵɵtext(51, \" Need help? Contact your system administrator \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(52, \"div\", 35);\n          i0.ɵɵelement(53, \"div\", 36)(54, \"div\", 37)(55, \"div\", 38);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"formGroup\", ctx.signinForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"agency\", \"required\") || ctx.hasError(\"agency\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"username\", \"required\") || ctx.hasError(\"username\", \"minlength\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"username\", \"required\") || ctx.hasError(\"username\", \"minlength\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"error\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵproperty(\"type\", ctx.showPassword ? \"text\" : \"password\");\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-label\", ctx.showPassword ? \"Hide password\" : \"Show password\");\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.showPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasError(\"password\", \"required\") || ctx.hasError(\"password\", \"minlength\"));\n          i0.ɵɵadvance(1);\n          i0.ɵɵclassProp(\"loading\", ctx.isLoading);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.signinForm.invalid);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName],\n      styles: [\"[_ngcontent-%COMP%]:root {\\n  --primary-color: #3b82f6;\\n  --primary-hover: #2563eb;\\n  --primary-light: #dbeafe;\\n  --secondary-color: #64748b;\\n  --success-color: #10b981;\\n  --error-color: #ef4444;\\n  --warning-color: #f59e0b;\\n  --background-color: #f8fafc;\\n  --surface-color: #ffffff;\\n  --text-primary: #1e293b;\\n  --text-secondary: #64748b;\\n  --text-muted: #94a3b8;\\n  --border-color: #e2e8f0;\\n  --border-focus: #3b82f6;\\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --radius-sm: 0.375rem;\\n  --radius-md: 0.5rem;\\n  --radius-lg: 0.75rem;\\n  --radius-xl: 1rem;\\n}\\n\\n.signin-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 1rem;\\n  position: relative;\\n  overflow: hidden;\\n}\\n@media (max-width: 640px) {\\n  .signin-container[_ngcontent-%COMP%] {\\n    padding: 0.5rem;\\n  }\\n}\\n\\n.background-decoration[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.decoration-circle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  animation: _ngcontent-%COMP%_float 6s ease-in-out infinite;\\n}\\n.decoration-circle.decoration-circle-1[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  top: 10%;\\n  left: 10%;\\n  animation-delay: 0s;\\n}\\n.decoration-circle.decoration-circle-2[_ngcontent-%COMP%] {\\n  width: 150px;\\n  height: 150px;\\n  top: 60%;\\n  right: 15%;\\n  animation-delay: 2s;\\n}\\n.decoration-circle.decoration-circle-3[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  bottom: 20%;\\n  left: 20%;\\n  animation-delay: 4s;\\n}\\n\\n@keyframes _ngcontent-%COMP%_float {\\n  0%, 100% {\\n    transform: translateY(0px);\\n  }\\n  50% {\\n    transform: translateY(-20px);\\n  }\\n}\\n.signin-card[_ngcontent-%COMP%] {\\n  background: var(--surface-color);\\n  border-radius: var(--radius-xl);\\n  box-shadow: var(--shadow-xl);\\n  padding: 2.5rem;\\n  width: 100%;\\n  max-width: 420px;\\n  position: relative;\\n  z-index: 1;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n@media (max-width: 640px) {\\n  .signin-card[_ngcontent-%COMP%] {\\n    padding: 2rem 1.5rem;\\n    max-width: 100%;\\n    margin: 0.5rem;\\n  }\\n}\\n\\n.signin-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 2rem;\\n}\\n\\n.logo-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 64px;\\n  height: 64px;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  border-radius: var(--radius-xl);\\n  color: white;\\n  box-shadow: var(--shadow-lg);\\n  margin: 0 auto;\\n}\\n\\n.signin-title[_ngcontent-%COMP%] {\\n  font-size: 1.875rem;\\n  font-weight: 700;\\n  color: var(--text-primary);\\n  margin: 0 0 0.5rem 0;\\n  line-height: 1.2;\\n}\\n\\n.signin-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: var(--text-secondary);\\n  margin: 0;\\n  line-height: 1.5;\\n}\\n\\n.error-container[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.875rem 1rem;\\n  background: #fef2f2;\\n  border: 1px solid #fecaca;\\n  border-radius: var(--radius-md);\\n  color: #dc2626;\\n  font-size: 0.875rem;\\n  line-height: 1.4;\\n  position: relative;\\n}\\n\\n.error-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n  color: var(--error-color);\\n}\\n\\n.error-close[_ngcontent-%COMP%] {\\n  background: none;\\n  border: none;\\n  color: #dc2626;\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: var(--radius-sm);\\n  margin-left: auto;\\n  flex-shrink: 0;\\n  transition: background-color 0.2s ease;\\n}\\n.error-close[_ngcontent-%COMP%]:hover {\\n  background: rgba(220, 38, 38, 0.1);\\n}\\n.error-close[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--error-color);\\n  outline-offset: 2px;\\n}\\n\\n.signin-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n\\n.form-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: var(--text-primary);\\n  margin: 0;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.form-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1rem 0.875rem 3rem;\\n  border: 2px solid var(--border-color);\\n  border-radius: var(--radius-md);\\n  font-size: 1rem;\\n  line-height: 1.5;\\n  color: var(--text-primary);\\n  background: var(--surface-color);\\n  transition: all 0.2s ease;\\n}\\n.form-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--text-muted);\\n}\\n.form-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--border-focus);\\n  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);\\n}\\n.form-input.error[_ngcontent-%COMP%] {\\n  border-color: var(--error-color);\\n}\\n.form-input.error[_ngcontent-%COMP%]:focus {\\n  border-color: var(--error-color);\\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\\n}\\n.form-input[_ngcontent-%COMP%]:disabled {\\n  background: #f8fafc;\\n  color: var(--text-muted);\\n  cursor: not-allowed;\\n}\\n\\n.input-icon[_ngcontent-%COMP%] {\\n  position: absolute;\\n  left: 1rem;\\n  color: var(--text-muted);\\n  pointer-events: none;\\n  z-index: 1;\\n}\\n\\n.password-toggle[_ngcontent-%COMP%] {\\n  position: absolute;\\n  right: 1rem;\\n  background: none;\\n  border: none;\\n  color: var(--text-muted);\\n  cursor: pointer;\\n  padding: 0.25rem;\\n  border-radius: var(--radius-sm);\\n  transition: color 0.2s ease;\\n  z-index: 1;\\n}\\n.password-toggle[_ngcontent-%COMP%]:hover {\\n  color: var(--text-secondary);\\n}\\n.password-toggle[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--border-focus);\\n  outline-offset: 2px;\\n}\\n\\n.error-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--error-color);\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n.signin-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 0.875rem 1.5rem;\\n  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));\\n  color: white;\\n  border: none;\\n  border-radius: var(--radius-md);\\n  font-size: 1rem;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  box-shadow: var(--shadow-sm);\\n  margin-top: 0.5rem;\\n}\\n.signin-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, var(--primary-hover), #1d4ed8);\\n  box-shadow: var(--shadow-md);\\n  transform: translateY(-1px);\\n}\\n.signin-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n.signin-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(0);\\n  box-shadow: var(--shadow-sm);\\n}\\n.signin-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--text-muted);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: none;\\n}\\n.signin-button.loading[_ngcontent-%COMP%] {\\n  background: var(--text-muted);\\n  cursor: not-allowed;\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 0.5rem;\\n}\\n\\n.loading-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.signin-footer[_ngcontent-%COMP%] {\\n  margin-top: 2rem;\\n  text-align: center;\\n}\\n\\n.footer-text[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: var(--text-muted);\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n\\n@media (max-width: 480px) {\\n  .signin-card[_ngcontent-%COMP%] {\\n    padding: 1.5rem 1rem;\\n  }\\n  .signin-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  .form-input[_ngcontent-%COMP%] {\\n    padding: 0.75rem 0.875rem 0.75rem 2.75rem;\\n  }\\n  .input-icon[_ngcontent-%COMP%] {\\n    left: 0.875rem;\\n  }\\n  .password-toggle[_ngcontent-%COMP%] {\\n    right: 0.875rem;\\n  }\\n}\\n@media (prefers-color-scheme: dark) {\\n  [_ngcontent-%COMP%]:root {\\n    --background-color: #0f172a;\\n    --surface-color: #1e293b;\\n    --text-primary: #f1f5f9;\\n    --text-secondary: #cbd5e1;\\n    --text-muted: #64748b;\\n    --border-color: #334155;\\n  }\\n  .signin-card[_ngcontent-%COMP%] {\\n    background: rgba(30, 41, 59, 0.95);\\n    border: 1px solid rgba(255, 255, 255, 0.1);\\n  }\\n  .form-input[_ngcontent-%COMP%] {\\n    background: var(--surface-color);\\n    border-color: var(--border-color);\\n  }\\n}\\n@media (prefers-reduced-motion: reduce) {\\n  *[_ngcontent-%COMP%] {\\n    animation-duration: 0.01ms !important;\\n    animation-iteration-count: 1 !important;\\n    transition-duration: 0.01ms !important;\\n  }\\n}\\n.signin-button[_ngcontent-%COMP%]:focus-visible, .form-input[_ngcontent-%COMP%]:focus-visible, .password-toggle[_ngcontent-%COMP%]:focus-visible, .error-close[_ngcontent-%COMP%]:focus-visible {\\n  outline: 2px solid var(--primary-color);\\n  outline-offset: 2px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ɵɵnamespaceHTML", "ɵɵtext", "ɵɵlistener", "SigninComponent_div_13_Template_button_click_8_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "clearError", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "errorMessage", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "ctx_r2", "ctx_r5", "SigninComponent", "constructor", "formBuilder", "authService", "router", "isLoading", "showPassword", "signinForm", "group", "agency", "required", "<PERSON><PERSON><PERSON><PERSON>", "username", "password", "ngOnInit", "isAuthenticated", "navigate", "onSubmit", "valid", "credentials", "value", "trim", "login", "subscribe", "next", "response", "success", "error", "message", "patchValue", "complete", "markFormGroupTouched", "togglePasswordVisibility", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFormControl", "controlName", "<PERSON><PERSON><PERSON><PERSON>", "errorType", "touched", "getFieldDisplayName", "<PERSON><PERSON><PERSON><PERSON>", "errors", "fieldNames", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "SigninComponent_Template", "rf", "ctx", "ɵɵtemplate", "SigninComponent_div_13_Template", "SigninComponent_Template_form_ngSubmit_14_listener", "SigninComponent_div_23_Template", "SigninComponent_div_32_Template", "SigninComponent_Template_button_click_42_listener", "SigninComponent__svg_svg_43_Template", "SigninComponent__svg_svg_44_Template", "SigninComponent_div_45_Template", "SigninComponent_span_47_Template", "SigninComponent_span_48_Template", "ɵɵproperty", "ɵɵclassProp", "ɵɵattribute", "invalid"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\signin\\signin.component.ts", "C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\components\\signin\\signin.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\nimport { LoginCredentials } from '../../models/auth.models';\n\n@Component({\n  selector: 'app-signin',\n  templateUrl: './signin.component.html',\n  styleUrls: ['./signin.component.scss']\n})\nexport class SigninComponent implements OnInit {\n  signinForm: FormGroup;\n  isLoading = false;\n  errorMessage = '';\n  showPassword = false;\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private authService: AuthService,\n    private router: Router\n  ) {\n    this.signinForm = this.formBuilder.group({\n      agency: ['', [Validators.required, Validators.minLength(2)]],\n      username: ['', [Validators.required, Validators.minLength(3)]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // If user is already authenticated, redirect to dashboard\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  /**\n   * Handle form submission\n   */\n  onSubmit(): void {\n    if (this.signinForm.valid) {\n      this.isLoading = true;\n      this.errorMessage = '';\n\n      const credentials: LoginCredentials = {\n        agency: this.signinForm.value.agency.trim(),\n        username: this.signinForm.value.username.trim(),\n        password: this.signinForm.value.password\n      };\n\n      this.authService.login(credentials).subscribe({\n        next: (response) => {\n          if (response.success) {\n            // Redirect to dashboard or intended route\n            this.router.navigate(['/dashboard']);\n          }\n        },\n        error: (error) => {\n          this.isLoading = false;\n          this.errorMessage = error.message || 'Login failed. Please try again.';\n          \n          // Clear password field on error\n          this.signinForm.patchValue({ password: '' });\n        },\n        complete: () => {\n          this.isLoading = false;\n        }\n      });\n    } else {\n      this.markFormGroupTouched();\n    }\n  }\n\n  /**\n   * Toggle password visibility\n   */\n  togglePasswordVisibility(): void {\n    this.showPassword = !this.showPassword;\n  }\n\n  /**\n   * Mark all form fields as touched to show validation errors\n   */\n  private markFormGroupTouched(): void {\n    Object.keys(this.signinForm.controls).forEach(key => {\n      const control = this.signinForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  /**\n   * Get form control for easier access in template\n   */\n  getFormControl(controlName: string) {\n    return this.signinForm.get(controlName);\n  }\n\n  /**\n   * Check if form control has error\n   */\n  hasError(controlName: string, errorType: string): boolean {\n    const control = this.getFormControl(controlName);\n    return !!(control?.hasError(errorType) && control?.touched);\n  }\n\n  /**\n   * Get error message for form control\n   */\n  getErrorMessage(controlName: string): string {\n    const control = this.getFormControl(controlName);\n    \n    if (control?.hasError('required')) {\n      return `${this.getFieldDisplayName(controlName)} is required`;\n    }\n    \n    if (control?.hasError('minlength')) {\n      const requiredLength = control.errors?.['minlength']?.requiredLength;\n      return `${this.getFieldDisplayName(controlName)} must be at least ${requiredLength} characters`;\n    }\n    \n    return '';\n  }\n\n  /**\n   * Get display name for form field\n   */\n  private getFieldDisplayName(controlName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      agency: 'Agency',\n      username: 'Username',\n      password: 'Password'\n    };\n    return fieldNames[controlName] || controlName;\n  }\n\n  /**\n   * Clear error message\n   */\n  clearError(): void {\n    this.errorMessage = '';\n  }\n}\n", "<div class=\"signin-container\">\n  <div class=\"signin-card\">\n    <!-- Header -->\n    <div class=\"signin-header\">\n      <div class=\"logo-container\">\n        <div class=\"logo\">\n          <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M12 2L2 7L12 12L22 7L12 2Z\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 17L12 22L22 17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <path d=\"M2 12L12 17L22 12\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n      </div>\n      <h1 class=\"signin-title\">Agency Portal</h1>\n      <p class=\"signin-subtitle\">Sign in to your agency account</p>\n    </div>\n\n    <!-- Error Message -->\n    <div class=\"error-container\" *ngIf=\"errorMessage\">\n      <div class=\"error-message\">\n        <svg class=\"error-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\" stroke=\"currentColor\" stroke-width=\"2\"/>\n        </svg>\n        <span>{{ errorMessage }}</span>\n        <button type=\"button\" class=\"error-close\" (click)=\"clearError()\">\n          <svg width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n        </button>\n      </div>\n    </div>\n\n    <!-- Sign-in Form -->\n    <form [formGroup]=\"signinForm\" (ngSubmit)=\"onSubmit()\" class=\"signin-form\">\n      <!-- Agency Field -->\n      <div class=\"form-group\">\n        <label for=\"agency\" class=\"form-label\">Agency Code</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"agency\"\n            formControlName=\"agency\"\n            class=\"form-input\"\n            [class.error]=\"hasError('agency', 'required') || hasError('agency', 'minlength')\"\n            placeholder=\"Enter your agency code\"\n            autocomplete=\"organization\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('agency', 'required') || hasError('agency', 'minlength')\">\n          {{ getErrorMessage('agency') }}\n        </div>\n      </div>\n\n      <!-- Username Field -->\n      <div class=\"form-group\">\n        <label for=\"username\" class=\"form-label\">Username</label>\n        <div class=\"input-container\">\n          <input\n            type=\"text\"\n            id=\"username\"\n            formControlName=\"username\"\n            class=\"form-input\"\n            [class.error]=\"hasError('username', 'required') || hasError('username', 'minlength')\"\n            placeholder=\"Enter your username\"\n            autocomplete=\"username\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            <circle cx=\"12\" cy=\"7\" r=\"4\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n          </svg>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('username', 'required') || hasError('username', 'minlength')\">\n          {{ getErrorMessage('username') }}\n        </div>\n      </div>\n\n      <!-- Password Field -->\n      <div class=\"form-group\">\n        <label for=\"password\" class=\"form-label\">Password</label>\n        <div class=\"input-container\">\n          <input\n            [type]=\"showPassword ? 'text' : 'password'\"\n            id=\"password\"\n            formControlName=\"password\"\n            class=\"form-input\"\n            [class.error]=\"hasError('password', 'required') || hasError('password', 'minlength')\"\n            placeholder=\"Enter your password\"\n            autocomplete=\"current-password\"\n          />\n          <svg class=\"input-icon\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <rect x=\"3\" y=\"11\" width=\"18\" height=\"11\" rx=\"2\" ry=\"2\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <circle cx=\"12\" cy=\"16\" r=\"1\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M7 11V7C7 5.67392 7.52678 4.40215 8.46447 3.46447C9.40215 2.52678 10.6739 2 12 2C13.3261 2 14.5979 2.52678 15.5355 3.46447C16.4732 4.40215 17 5.67392 17 7V11\" stroke=\"currentColor\" stroke-width=\"2\"/>\n          </svg>\n          <button\n            type=\"button\"\n            class=\"password-toggle\"\n            (click)=\"togglePasswordVisibility()\"\n            [attr.aria-label]=\"showPassword ? 'Hide password' : 'Show password'\"\n          >\n            <svg *ngIf=\"!showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            </svg>\n            <svg *ngIf=\"showPassword\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path d=\"M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.028 7.66607 6.17 6.17\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <path d=\"M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n              <circle cx=\"12\" cy=\"12\" r=\"3\" stroke=\"currentColor\" stroke-width=\"2\"/>\n              <path d=\"M1 1L23 23\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n            </svg>\n          </button>\n        </div>\n        <div class=\"error-text\" *ngIf=\"hasError('password', 'required') || hasError('password', 'minlength')\">\n          {{ getErrorMessage('password') }}\n        </div>\n      </div>\n\n      <!-- Submit Button -->\n      <button\n        type=\"submit\"\n        class=\"signin-button\"\n        [disabled]=\"isLoading || signinForm.invalid\"\n        [class.loading]=\"isLoading\"\n      >\n        <span *ngIf=\"!isLoading\">Sign In</span>\n        <span *ngIf=\"isLoading\" class=\"loading-content\">\n          <svg class=\"loading-spinner\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n            <path d=\"M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" stroke-width=\"2\"/>\n            <path d=\"M12 3C16.9706 3 21 7.02944 21 12\" stroke=\"url(#spinner-gradient)\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n            <defs>\n              <linearGradient id=\"spinner-gradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n                <stop offset=\"0%\" style=\"stop-color:currentColor;stop-opacity:0\" />\n                <stop offset=\"100%\" style=\"stop-color:currentColor;stop-opacity:1\" />\n              </linearGradient>\n            </defs>\n          </svg>\n          Signing in...\n        </span>\n      </button>\n    </form>\n\n    <!-- Footer -->\n    <div class=\"signin-footer\">\n      <p class=\"footer-text\">\n        Need help? Contact your system administrator\n      </p>\n    </div>\n  </div>\n\n  <!-- Background decoration -->\n  <div class=\"background-decoration\">\n    <div class=\"decoration-circle decoration-circle-1\"></div>\n    <div class=\"decoration-circle decoration-circle-2\"></div>\n    <div class=\"decoration-circle decoration-circle-3\"></div>\n  </div>\n</div>\n"], "mappings": "AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;ICiB/DC,EAAA,CAAAC,cAAA,cAAkD;IAE9CD,EAAA,CAAAE,cAAA,EAAkH;IAAlHF,EAAA,CAAAC,cAAA,cAAkH;IAChHD,EAAA,CAAAG,SAAA,iBAAuE;IAGzEH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAK,eAAA,EAAM;IAANL,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAM,MAAA,GAAkB;IAAAN,EAAA,CAAAI,YAAA,EAAO;IAC/BJ,EAAA,CAAAC,cAAA,iBAAiE;IAAvBD,EAAA,CAAAO,UAAA,mBAAAC,wDAAA;MAAAR,EAAA,CAAAS,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAASZ,EAAA,CAAAa,WAAA,CAAAF,MAAA,CAAAG,UAAA,EAAY;IAAA,EAAC;IAC9Dd,EAAA,CAAAE,cAAA,EAA+F;IAA/FF,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAG,SAAA,gBAA4E;IAE9EH,EAAA,CAAAI,YAAA,EAAM;;;;IALFJ,EAAA,CAAAe,SAAA,GAAkB;IAAlBf,EAAA,CAAAgB,iBAAA,CAAAC,MAAA,CAAAC,YAAA,CAAkB;;;;;;IA8BxBlB,EAAA,CAAAK,eAAA,EAAkG;IAAlGL,EAAA,CAAAC,cAAA,cAAkG;IAChGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAC,MAAA,CAAAC,eAAA,gBACF;;;;;;IAqBArB,EAAA,CAAAK,eAAA,EAAsG;IAAtGL,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAG,MAAA,CAAAD,eAAA,kBACF;;;;;IA2BIrB,EAAA,CAAAE,cAAA,EAAqH;IAArHF,EAAA,CAAAC,cAAA,cAAqH;IACnHD,EAAA,CAAAG,SAAA,eAAoG;IAEtGH,EAAA,CAAAI,YAAA,EAAM;;;;;IACNJ,EAAA,CAAAE,cAAA,EAAoH;IAApHF,EAAA,CAAAC,cAAA,cAAoH;IAClHD,EAAA,CAAAG,SAAA,eAA0M;IAI5MH,EAAA,CAAAI,YAAA,EAAM;;;;;IAGVJ,EAAA,CAAAC,cAAA,cAAsG;IACpGD,EAAA,CAAAM,MAAA,GACF;IAAAN,EAAA,CAAAI,YAAA,EAAM;;;;IADJJ,EAAA,CAAAe,SAAA,GACF;IADEf,EAAA,CAAAmB,kBAAA,MAAAI,MAAA,CAAAF,eAAA,kBACF;;;;;IAUArB,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAM,MAAA,cAAO;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;;;IACvCJ,EAAA,CAAAC,cAAA,eAAgD;IAC9CD,EAAA,CAAAE,cAAA,EAAuH;IAAvHF,EAAA,CAAAC,cAAA,cAAuH;IACrHD,EAAA,CAAAG,SAAA,eAAoK;IAEpKH,EAAA,CAAAC,cAAA,WAAM;IAEFD,EAAA,CAAAG,SAAA,eAAmE;IAErEH,EAAA,CAAAI,YAAA,EAAiB;IAGrBJ,EAAA,CAAAM,MAAA,sBACF;IAAAN,EAAA,CAAAI,YAAA,EAAO;;;ADrIf,OAAM,MAAOoB,eAAe;EAM1BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAPhB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAX,YAAY,GAAG,EAAE;IACjB,KAAAY,YAAY,GAAG,KAAK;IAOlB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACL,WAAW,CAACM,KAAK,CAAC;MACvCC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC5DC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACX,WAAW,CAACY,eAAe,EAAE,EAAE;MACtC,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACV,UAAU,CAACW,KAAK,EAAE;MACzB,IAAI,CAACb,SAAS,GAAG,IAAI;MACrB,IAAI,CAACX,YAAY,GAAG,EAAE;MAEtB,MAAMyB,WAAW,GAAqB;QACpCV,MAAM,EAAE,IAAI,CAACF,UAAU,CAACa,KAAK,CAACX,MAAM,CAACY,IAAI,EAAE;QAC3CT,QAAQ,EAAE,IAAI,CAACL,UAAU,CAACa,KAAK,CAACR,QAAQ,CAACS,IAAI,EAAE;QAC/CR,QAAQ,EAAE,IAAI,CAACN,UAAU,CAACa,KAAK,CAACP;OACjC;MAED,IAAI,CAACV,WAAW,CAACmB,KAAK,CAACH,WAAW,CAAC,CAACI,SAAS,CAAC;QAC5CC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAIA,QAAQ,CAACC,OAAO,EAAE;YACpB;YACA,IAAI,CAACtB,MAAM,CAACY,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;QAExC,CAAC;QACDW,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACtB,SAAS,GAAG,KAAK;UACtB,IAAI,CAACX,YAAY,GAAGiC,KAAK,CAACC,OAAO,IAAI,iCAAiC;UAEtE;UACA,IAAI,CAACrB,UAAU,CAACsB,UAAU,CAAC;YAAEhB,QAAQ,EAAE;UAAE,CAAE,CAAC;QAC9C,CAAC;QACDiB,QAAQ,EAAEA,CAAA,KAAK;UACb,IAAI,CAACzB,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;KACH,MAAM;MACL,IAAI,CAAC0B,oBAAoB,EAAE;;EAE/B;EAEA;;;EAGAC,wBAAwBA,CAAA;IACtB,IAAI,CAAC1B,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;EACxC;EAEA;;;EAGQyB,oBAAoBA,CAAA;IAC1BE,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC3B,UAAU,CAAC4B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MAClD,MAAMC,OAAO,GAAG,IAAI,CAAC/B,UAAU,CAACgC,GAAG,CAACF,GAAG,CAAC;MACxCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA;;;EAGAC,cAAcA,CAACC,WAAmB;IAChC,OAAO,IAAI,CAACnC,UAAU,CAACgC,GAAG,CAACG,WAAW,CAAC;EACzC;EAEA;;;EAGAC,QAAQA,CAACD,WAAmB,EAAEE,SAAiB;IAC7C,MAAMN,OAAO,GAAG,IAAI,CAACG,cAAc,CAACC,WAAW,CAAC;IAChD,OAAO,CAAC,EAAEJ,OAAO,EAAEK,QAAQ,CAACC,SAAS,CAAC,IAAIN,OAAO,EAAEO,OAAO,CAAC;EAC7D;EAEA;;;EAGAhD,eAAeA,CAAC6C,WAAmB;IACjC,MAAMJ,OAAO,GAAG,IAAI,CAACG,cAAc,CAACC,WAAW,CAAC;IAEhD,IAAIJ,OAAO,EAAEK,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACG,mBAAmB,CAACJ,WAAW,CAAC,cAAc;;IAG/D,IAAIJ,OAAO,EAAEK,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMI,cAAc,GAAGT,OAAO,CAACU,MAAM,GAAG,WAAW,CAAC,EAAED,cAAc;MACpE,OAAO,GAAG,IAAI,CAACD,mBAAmB,CAACJ,WAAW,CAAC,qBAAqBK,cAAc,aAAa;;IAGjG,OAAO,EAAE;EACX;EAEA;;;EAGQD,mBAAmBA,CAACJ,WAAmB;IAC7C,MAAMO,UAAU,GAA8B;MAC5CxC,MAAM,EAAE,QAAQ;MAChBG,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX;IACD,OAAOoC,UAAU,CAACP,WAAW,CAAC,IAAIA,WAAW;EAC/C;EAEA;;;EAGApD,UAAUA,CAAA;IACR,IAAI,CAACI,YAAY,GAAG,EAAE;EACxB;;;uBAjIWM,eAAe,EAAAxB,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA5E,EAAA,CAAA0E,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA9E,EAAA,CAAA0E,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAfxD,eAAe;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCX5BvF,EAAA,CAAAC,cAAA,aAA8B;UAMpBD,EAAA,CAAAE,cAAA,EAA+F;UAA/FF,EAAA,CAAAC,cAAA,aAA+F;UAC7FD,EAAA,CAAAG,SAAA,cAA4H;UAG9HH,EAAA,CAAAI,YAAA,EAAM;UAGVJ,EAAA,CAAAK,eAAA,EAAyB;UAAzBL,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAM,MAAA,qBAAa;UAAAN,EAAA,CAAAI,YAAA,EAAK;UAC3CJ,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAM,MAAA,sCAA8B;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAI/DJ,EAAA,CAAAyF,UAAA,KAAAC,+BAAA,mBAeM;UAGN1F,EAAA,CAAAC,cAAA,gBAA2E;UAA5CD,EAAA,CAAAO,UAAA,sBAAAoF,mDAAA;YAAA,OAAYH,GAAA,CAAA/C,QAAA,EAAU;UAAA,EAAC;UAEpDzC,EAAA,CAAAC,cAAA,eAAwB;UACiBD,EAAA,CAAAM,MAAA,mBAAW;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UAC1DJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAsQ;UAExQH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAyF,UAAA,KAAAG,+BAAA,kBAEM;UACR5F,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAwB;UAAxBL,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAsQ;UAExQH,EAAA,CAAAI,YAAA,EAAM;UAERJ,EAAA,CAAAyF,UAAA,KAAAI,+BAAA,kBAEM;UACR7F,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAK,eAAA,EAAwB;UAAxBL,EAAA,CAAAC,cAAA,eAAwB;UACmBD,EAAA,CAAAM,MAAA,gBAAQ;UAAAN,EAAA,CAAAI,YAAA,EAAQ;UACzDJ,EAAA,CAAAC,cAAA,eAA6B;UAC3BD,EAAA,CAAAG,SAAA,iBAQE;UACFH,EAAA,CAAAE,cAAA,EAAkH;UAAlHF,EAAA,CAAAC,cAAA,eAAkH;UAChHD,EAAA,CAAAG,SAAA,gBAAgG;UAGlGH,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAK,eAAA,EAKC;UALDL,EAAA,CAAAC,cAAA,kBAKC;UAFCD,EAAA,CAAAO,UAAA,mBAAAuF,kDAAA;YAAA,OAASN,GAAA,CAAAhC,wBAAA,EAA0B;UAAA,EAAC;UAGpCxD,EAAA,CAAAyF,UAAA,KAAAM,oCAAA,kBAGM;UACN/F,EAAA,CAAAyF,UAAA,KAAAO,oCAAA,kBAKM;UACRhG,EAAA,CAAAI,YAAA,EAAS;UAEXJ,EAAA,CAAAyF,UAAA,KAAAQ,+BAAA,kBAEM;UACRjG,EAAA,CAAAI,YAAA,EAAM;UAGNJ,EAAA,CAAAC,cAAA,kBAKC;UACCD,EAAA,CAAAyF,UAAA,KAAAS,gCAAA,mBAAuC;UACvClG,EAAA,CAAAyF,UAAA,KAAAU,gCAAA,mBAYO;UACTnG,EAAA,CAAAI,YAAA,EAAS;UAIXJ,EAAA,CAAAC,cAAA,eAA2B;UAEvBD,EAAA,CAAAM,MAAA,sDACF;UAAAN,EAAA,CAAAI,YAAA,EAAI;UAKRJ,EAAA,CAAAC,cAAA,eAAmC;UACjCD,EAAA,CAAAG,SAAA,eAAyD;UAG3DH,EAAA,CAAAI,YAAA,EAAM;;;UA/I0BJ,EAAA,CAAAe,SAAA,IAAkB;UAAlBf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAAtE,YAAA,CAAkB;UAkB1ClB,EAAA,CAAAe,SAAA,GAAwB;UAAxBf,EAAA,CAAAoG,UAAA,cAAAZ,GAAA,CAAAzD,UAAA,CAAwB;UAUtB/B,EAAA,CAAAe,SAAA,GAAiF;UAAjFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,0BAAAqB,GAAA,CAAArB,QAAA,wBAAiF;UAS5DnE,EAAA,CAAAe,SAAA,GAAuE;UAAvEf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,0BAAAqB,GAAA,CAAArB,QAAA,wBAAuE;UAc5FnE,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAAqF;UAShEnE,EAAA,CAAAe,SAAA,GAA2E;UAA3Ef,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAA2E;UAchGnE,EAAA,CAAAe,SAAA,GAAqF;UAArFf,EAAA,CAAAqG,WAAA,UAAAb,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAAqF;UAJrFnE,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA1D,YAAA,uBAA2C;UAiB3C9B,EAAA,CAAAe,SAAA,GAAoE;UAApEf,EAAA,CAAAsG,WAAA,eAAAd,GAAA,CAAA1D,YAAA,qCAAoE;UAE9D9B,EAAA,CAAAe,SAAA,GAAmB;UAAnBf,EAAA,CAAAoG,UAAA,UAAAZ,GAAA,CAAA1D,YAAA,CAAmB;UAInB9B,EAAA,CAAAe,SAAA,GAAkB;UAAlBf,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA1D,YAAA,CAAkB;UAQH9B,EAAA,CAAAe,SAAA,GAA2E;UAA3Ef,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAArB,QAAA,4BAAAqB,GAAA,CAAArB,QAAA,0BAA2E;UAUpGnE,EAAA,CAAAe,SAAA,GAA2B;UAA3Bf,EAAA,CAAAqG,WAAA,YAAAb,GAAA,CAAA3D,SAAA,CAA2B;UAD3B7B,EAAA,CAAAoG,UAAA,aAAAZ,GAAA,CAAA3D,SAAA,IAAA2D,GAAA,CAAAzD,UAAA,CAAAwE,OAAA,CAA4C;UAGrCvG,EAAA,CAAAe,SAAA,GAAgB;UAAhBf,EAAA,CAAAoG,UAAA,UAAAZ,GAAA,CAAA3D,SAAA,CAAgB;UAChB7B,EAAA,CAAAe,SAAA,GAAe;UAAff,EAAA,CAAAoG,UAAA,SAAAZ,GAAA,CAAA3D,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}