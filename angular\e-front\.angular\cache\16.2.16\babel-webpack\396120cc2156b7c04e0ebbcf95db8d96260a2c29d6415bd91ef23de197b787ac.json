{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = 'http://localhost:8080/auth';\n    this.TOKEN_KEY = 'auth_token';\n    this.USER_KEY = 'user_data';\n    this.isAuthenticatedSubject = new BehaviorSubject(this.hasToken());\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.currentUserSubject = new BehaviorSubject(this.getCurrentUser());\n    this.currentUser$ = this.currentUserSubject.asObservable();\n  }\n  /**\n   * Authenticate user with agency credentials\n   */\n  login(credentials) {\n    const authRequest = {\n      Agency: credentials.agency,\n      User: credentials.username,\n      Password: credentials.password\n    };\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n    return this.http.post(`${this.API_URL}/login`, authRequest, {\n      headers\n    }).pipe(map(response => {\n      if (response.header.success && response.body.token) {\n        // Store token and user data\n        this.setToken(response.body.token);\n        const userData = {\n          id: response.body.user.id,\n          name: response.body.user.name,\n          email: response.body.user.email,\n          agency: response.body.agency.name,\n          agencyCode: response.body.agency.code\n        };\n        this.setUserData(userData);\n        // Update subjects\n        this.isAuthenticatedSubject.next(true);\n        this.currentUserSubject.next(userData);\n        return {\n          success: true,\n          token: response.body.token,\n          user: userData,\n          message: 'Login successful'\n        };\n      } else {\n        throw new Error(response.header.responseMessage || 'Authentication failed');\n      }\n    }), catchError(error => {\n      console.error('Login error:', error);\n      let errorMessage = 'An error occurred during login';\n      if (error.error?.header?.responseMessage) {\n        errorMessage = error.error.header.responseMessage;\n      } else if (error.message) {\n        errorMessage = error.message;\n      } else if (error.status === 401) {\n        errorMessage = 'Invalid credentials';\n      } else if (error.status === 0) {\n        errorMessage = 'Unable to connect to server';\n      }\n      return throwError(() => ({\n        success: false,\n        message: errorMessage\n      }));\n    }));\n  }\n  /**\n   * Logout user\n   */\n  logout() {\n    this.removeToken();\n    this.removeUserData();\n    this.isAuthenticatedSubject.next(false);\n    this.currentUserSubject.next(null);\n  }\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated() {\n    return this.hasToken() && !this.isTokenExpired();\n  }\n  /**\n   * Get current user data\n   */\n  getCurrentUser() {\n    const userData = localStorage.getItem(this.USER_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n  /**\n   * Get authentication token\n   */\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n  /**\n   * Set authentication token\n   */\n  setToken(token) {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n  /**\n   * Remove authentication token\n   */\n  removeToken() {\n    localStorage.removeItem(this.TOKEN_KEY);\n  }\n  /**\n   * Set user data\n   */\n  setUserData(userData) {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));\n  }\n  /**\n   * Remove user data\n   */\n  removeUserData() {\n    localStorage.removeItem(this.USER_KEY);\n  }\n  /**\n   * Check if token exists\n   */\n  hasToken() {\n    return !!localStorage.getItem(this.TOKEN_KEY);\n  }\n  /**\n   * Check if token is expired (basic check)\n   * Note: This is a simple implementation. In production, you should decode the JWT token\n   * and check the expiration time properly.\n   */\n  isTokenExpired() {\n    // For now, we'll assume the token is valid if it exists\n    // In a real implementation, you would decode the JWT and check the exp claim\n    return false;\n  }\n  /**\n   * Get authorization headers for API calls\n   */\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "map", "catchError", "AuthService", "constructor", "http", "API_URL", "TOKEN_KEY", "USER_KEY", "isAuthenticatedSubject", "hasToken", "isAuthenticated$", "asObservable", "currentUserSubject", "getCurrentUser", "currentUser$", "login", "credentials", "authRequest", "Agency", "agency", "User", "username", "Password", "password", "headers", "post", "pipe", "response", "header", "success", "body", "token", "setToken", "userData", "id", "user", "name", "email", "agencyCode", "code", "setUserData", "next", "message", "Error", "responseMessage", "error", "console", "errorMessage", "status", "logout", "removeToken", "removeUserData", "isAuthenticated", "isTokenExpired", "localStorage", "getItem", "JSON", "parse", "getToken", "setItem", "removeItem", "stringify", "getAuthHeaders", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders } from '@angular/common/http';\nimport { Observable, BehaviorSubject, throwError } from 'rxjs';\nimport { map, catchError, tap } from 'rxjs/operators';\nimport { AuthRequest, AuthResponse, LoginCredentials, LoginResponse } from '../models/auth.models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = 'http://localhost:8080/auth';\n  private readonly TOKEN_KEY = 'auth_token';\n  private readonly USER_KEY = 'user_data';\n\n  private isAuthenticatedSubject = new BehaviorSubject<boolean>(this.hasToken());\n  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n  private currentUserSubject = new BehaviorSubject<any>(this.getCurrentUser());\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * Authenticate user with agency credentials\n   */\n  login(credentials: LoginCredentials): Observable<LoginResponse> {\n    const authRequest: AuthRequest = {\n      Agency: credentials.agency,\n      User: credentials.username,\n      Password: credentials.password\n    };\n\n    const headers = new HttpHeaders({\n      'Content-Type': 'application/json'\n    });\n\n    return this.http.post<AuthResponse>(`${this.API_URL}/login`, authRequest, { headers })\n      .pipe(\n        map(response => {\n          if (response.header.success && response.body.token) {\n            // Store token and user data\n            this.setToken(response.body.token);\n            const userData = {\n              id: response.body.user.id,\n              name: response.body.user.name,\n              email: response.body.user.email,\n              agency: response.body.agency.name,\n              agencyCode: response.body.agency.code\n            };\n            this.setUserData(userData);\n            \n            // Update subjects\n            this.isAuthenticatedSubject.next(true);\n            this.currentUserSubject.next(userData);\n\n            return {\n              success: true,\n              token: response.body.token,\n              user: userData,\n              message: 'Login successful'\n            };\n          } else {\n            throw new Error(response.header.responseMessage || 'Authentication failed');\n          }\n        }),\n        catchError(error => {\n          console.error('Login error:', error);\n          let errorMessage = 'An error occurred during login';\n          \n          if (error.error?.header?.responseMessage) {\n            errorMessage = error.error.header.responseMessage;\n          } else if (error.message) {\n            errorMessage = error.message;\n          } else if (error.status === 401) {\n            errorMessage = 'Invalid credentials';\n          } else if (error.status === 0) {\n            errorMessage = 'Unable to connect to server';\n          }\n\n          return throwError(() => ({\n            success: false,\n            message: errorMessage\n          }));\n        })\n      );\n  }\n\n  /**\n   * Logout user\n   */\n  logout(): void {\n    this.removeToken();\n    this.removeUserData();\n    this.isAuthenticatedSubject.next(false);\n    this.currentUserSubject.next(null);\n  }\n\n  /**\n   * Check if user is authenticated\n   */\n  isAuthenticated(): boolean {\n    return this.hasToken() && !this.isTokenExpired();\n  }\n\n  /**\n   * Get current user data\n   */\n  getCurrentUser(): any {\n    const userData = localStorage.getItem(this.USER_KEY);\n    return userData ? JSON.parse(userData) : null;\n  }\n\n  /**\n   * Get authentication token\n   */\n  getToken(): string | null {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set authentication token\n   */\n  private setToken(token: string): void {\n    localStorage.setItem(this.TOKEN_KEY, token);\n  }\n\n  /**\n   * Remove authentication token\n   */\n  private removeToken(): void {\n    localStorage.removeItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Set user data\n   */\n  private setUserData(userData: any): void {\n    localStorage.setItem(this.USER_KEY, JSON.stringify(userData));\n  }\n\n  /**\n   * Remove user data\n   */\n  private removeUserData(): void {\n    localStorage.removeItem(this.USER_KEY);\n  }\n\n  /**\n   * Check if token exists\n   */\n  private hasToken(): boolean {\n    return !!localStorage.getItem(this.TOKEN_KEY);\n  }\n\n  /**\n   * Check if token is expired (basic check)\n   * Note: This is a simple implementation. In production, you should decode the JWT token\n   * and check the expiration time properly.\n   */\n  private isTokenExpired(): boolean {\n    // For now, we'll assume the token is valid if it exists\n    // In a real implementation, you would decode the JWT and check the exp claim\n    return false;\n  }\n\n  /**\n   * Get authorization headers for API calls\n   */\n  getAuthHeaders(): HttpHeaders {\n    const token = this.getToken();\n    return new HttpHeaders({\n      'Authorization': `Bearer ${token}`,\n      'Content-Type': 'application/json'\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAAqBC,eAAe,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAa,gBAAgB;;;AAMrD,OAAM,MAAOC,WAAW;EAWtBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAVP,KAAAC,OAAO,GAAG,4BAA4B;IACtC,KAAAC,SAAS,GAAG,YAAY;IACxB,KAAAC,QAAQ,GAAG,WAAW;IAE/B,KAAAC,sBAAsB,GAAG,IAAIV,eAAe,CAAU,IAAI,CAACW,QAAQ,EAAE,CAAC;IACvE,KAAAC,gBAAgB,GAAG,IAAI,CAACF,sBAAsB,CAACG,YAAY,EAAE;IAE5D,KAAAC,kBAAkB,GAAG,IAAId,eAAe,CAAM,IAAI,CAACe,cAAc,EAAE,CAAC;IACrE,KAAAC,YAAY,GAAG,IAAI,CAACF,kBAAkB,CAACD,YAAY,EAAE;EAErB;EAEvC;;;EAGAI,KAAKA,CAACC,WAA6B;IACjC,MAAMC,WAAW,GAAgB;MAC/BC,MAAM,EAAEF,WAAW,CAACG,MAAM;MAC1BC,IAAI,EAAEJ,WAAW,CAACK,QAAQ;MAC1BC,QAAQ,EAAEN,WAAW,CAACO;KACvB;IAED,MAAMC,OAAO,GAAG,IAAI3B,WAAW,CAAC;MAC9B,cAAc,EAAE;KACjB,CAAC;IAEF,OAAO,IAAI,CAACO,IAAI,CAACqB,IAAI,CAAe,GAAG,IAAI,CAACpB,OAAO,QAAQ,EAAEY,WAAW,EAAE;MAAEO;IAAO,CAAE,CAAC,CACnFE,IAAI,CACH1B,GAAG,CAAC2B,QAAQ,IAAG;MACb,IAAIA,QAAQ,CAACC,MAAM,CAACC,OAAO,IAAIF,QAAQ,CAACG,IAAI,CAACC,KAAK,EAAE;QAClD;QACA,IAAI,CAACC,QAAQ,CAACL,QAAQ,CAACG,IAAI,CAACC,KAAK,CAAC;QAClC,MAAME,QAAQ,GAAG;UACfC,EAAE,EAAEP,QAAQ,CAACG,IAAI,CAACK,IAAI,CAACD,EAAE;UACzBE,IAAI,EAAET,QAAQ,CAACG,IAAI,CAACK,IAAI,CAACC,IAAI;UAC7BC,KAAK,EAAEV,QAAQ,CAACG,IAAI,CAACK,IAAI,CAACE,KAAK;UAC/BlB,MAAM,EAAEQ,QAAQ,CAACG,IAAI,CAACX,MAAM,CAACiB,IAAI;UACjCE,UAAU,EAAEX,QAAQ,CAACG,IAAI,CAACX,MAAM,CAACoB;SAClC;QACD,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;QAE1B;QACA,IAAI,CAACzB,sBAAsB,CAACiC,IAAI,CAAC,IAAI,CAAC;QACtC,IAAI,CAAC7B,kBAAkB,CAAC6B,IAAI,CAACR,QAAQ,CAAC;QAEtC,OAAO;UACLJ,OAAO,EAAE,IAAI;UACbE,KAAK,EAAEJ,QAAQ,CAACG,IAAI,CAACC,KAAK;UAC1BI,IAAI,EAAEF,QAAQ;UACdS,OAAO,EAAE;SACV;OACF,MAAM;QACL,MAAM,IAAIC,KAAK,CAAChB,QAAQ,CAACC,MAAM,CAACgB,eAAe,IAAI,uBAAuB,CAAC;;IAE/E,CAAC,CAAC,EACF3C,UAAU,CAAC4C,KAAK,IAAG;MACjBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,IAAIE,YAAY,GAAG,gCAAgC;MAEnD,IAAIF,KAAK,CAACA,KAAK,EAAEjB,MAAM,EAAEgB,eAAe,EAAE;QACxCG,YAAY,GAAGF,KAAK,CAACA,KAAK,CAACjB,MAAM,CAACgB,eAAe;OAClD,MAAM,IAAIC,KAAK,CAACH,OAAO,EAAE;QACxBK,YAAY,GAAGF,KAAK,CAACH,OAAO;OAC7B,MAAM,IAAIG,KAAK,CAACG,MAAM,KAAK,GAAG,EAAE;QAC/BD,YAAY,GAAG,qBAAqB;OACrC,MAAM,IAAIF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;QAC7BD,YAAY,GAAG,6BAA6B;;MAG9C,OAAOhD,UAAU,CAAC,OAAO;QACvB8B,OAAO,EAAE,KAAK;QACda,OAAO,EAAEK;OACV,CAAC,CAAC;IACL,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAE,MAAMA,CAAA;IACJ,IAAI,CAACC,WAAW,EAAE;IAClB,IAAI,CAACC,cAAc,EAAE;IACrB,IAAI,CAAC3C,sBAAsB,CAACiC,IAAI,CAAC,KAAK,CAAC;IACvC,IAAI,CAAC7B,kBAAkB,CAAC6B,IAAI,CAAC,IAAI,CAAC;EACpC;EAEA;;;EAGAW,eAAeA,CAAA;IACb,OAAO,IAAI,CAAC3C,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC4C,cAAc,EAAE;EAClD;EAEA;;;EAGAxC,cAAcA,CAAA;IACZ,MAAMoB,QAAQ,GAAGqB,YAAY,CAACC,OAAO,CAAC,IAAI,CAAChD,QAAQ,CAAC;IACpD,OAAO0B,QAAQ,GAAGuB,IAAI,CAACC,KAAK,CAACxB,QAAQ,CAAC,GAAG,IAAI;EAC/C;EAEA;;;EAGAyB,QAAQA,CAAA;IACN,OAAOJ,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjD,SAAS,CAAC;EAC7C;EAEA;;;EAGQ0B,QAAQA,CAACD,KAAa;IAC5BuB,YAAY,CAACK,OAAO,CAAC,IAAI,CAACrD,SAAS,EAAEyB,KAAK,CAAC;EAC7C;EAEA;;;EAGQmB,WAAWA,CAAA;IACjBI,YAAY,CAACM,UAAU,CAAC,IAAI,CAACtD,SAAS,CAAC;EACzC;EAEA;;;EAGQkC,WAAWA,CAACP,QAAa;IAC/BqB,YAAY,CAACK,OAAO,CAAC,IAAI,CAACpD,QAAQ,EAAEiD,IAAI,CAACK,SAAS,CAAC5B,QAAQ,CAAC,CAAC;EAC/D;EAEA;;;EAGQkB,cAAcA,CAAA;IACpBG,YAAY,CAACM,UAAU,CAAC,IAAI,CAACrD,QAAQ,CAAC;EACxC;EAEA;;;EAGQE,QAAQA,CAAA;IACd,OAAO,CAAC,CAAC6C,YAAY,CAACC,OAAO,CAAC,IAAI,CAACjD,SAAS,CAAC;EAC/C;EAEA;;;;;EAKQ+C,cAAcA,CAAA;IACpB;IACA;IACA,OAAO,KAAK;EACd;EAEA;;;EAGAS,cAAcA,CAAA;IACZ,MAAM/B,KAAK,GAAG,IAAI,CAAC2B,QAAQ,EAAE;IAC7B,OAAO,IAAI7D,WAAW,CAAC;MACrB,eAAe,EAAE,UAAUkC,KAAK,EAAE;MAClC,cAAc,EAAE;KACjB,CAAC;EACJ;;;uBArKW7B,WAAW,EAAA6D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXhE,WAAW;MAAAiE,OAAA,EAAXjE,WAAW,CAAAkE,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}