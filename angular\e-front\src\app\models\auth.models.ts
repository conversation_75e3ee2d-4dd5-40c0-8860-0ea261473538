export interface AuthRequest {
  Agency: string;
  User: string;
  Password: string;
}

export interface AuthResponse {
  header: {
    requestId: string;
    success: boolean;
    responseMessage: string;
  };
  body: {
    token: string;
    expiresOn: string;
    agency: {
      id: number;
      name: string;
      code: string;
    };
    user: {
      id: number;
      name: string;
      email: string;
    };
  };
}

export interface LoginCredentials {
  agency: string;
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  token?: string;
  message?: string;
  user?: {
    id: number;
    name: string;
    email: string;
    agency: string;
  };
}
