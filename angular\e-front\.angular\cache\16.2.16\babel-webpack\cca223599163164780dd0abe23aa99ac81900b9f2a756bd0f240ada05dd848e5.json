{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, SigninComponent, DashboardComponent],\n    imports: [BrowserModule, AppRoutingModule, ReactiveFormsModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "ReactiveFormsModule", "HttpClientModule", "AppRoutingModule", "AppComponent", "SigninComponent", "DashboardComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\dhia b2b\\angular\\e-front\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { HttpClientModule } from '@angular/common/http';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { SigninComponent } from './components/signin/signin.component';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    SigninComponent,\n    DashboardComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    ReactiveFormsModule,\n    HttpClientModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,sCAAsC;AACtE,SAASC,kBAAkB,QAAQ,4CAA4C;;AAiB/E,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRJ,YAAY;IAAA;EAAA;;;gBANtBJ,aAAa,EACbG,gBAAgB,EAChBF,mBAAmB,EACnBC,gBAAgB;IAAA;EAAA;;;2EAKPK,SAAS;IAAAE,YAAA,GAblBL,YAAY,EACZC,eAAe,EACfC,kBAAkB;IAAAI,OAAA,GAGlBV,aAAa,EACbG,gBAAgB,EAChBF,mBAAmB,EACnBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}